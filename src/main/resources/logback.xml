<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="LOG_LOCATION" value="logs/heal-riverbed-connector.log"/>
    <property name="CORE_LOG_LOCATION" value="logs/heal-riverbed-connector-core.log"/>
    <property name="STATS_LOG_LOCATION" value="logs/heal-riverbed-connector-stats.log"/>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_LOCATION}</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/heal-riverbed-connector_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
            <maxHistory>10</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="CORE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${CORE_LOG_LOCATION}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/heal-riverbed-connector-core_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
            <maxHistory>10</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="STATS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${STATS_LOG_LOCATION}</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/heal-riverbed-connector-stats_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>5MB</maxFileSize>
            <totalSizeCap>500MB</totalSizeCap>
            <maxHistory>10</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.heal.etladapter" level="DEBUG" additivity="false">
        <appender-ref ref="CORE_FILE"/>
    </logger>

        <logger name="com.heal.riverbed" level="DEBUG" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.heal.riverbed.schedulers.StatisticsScheduler" level="INFO" additivity="false">
        <appender-ref ref="STATS_FILE"/>
    </logger>

    <logger name="com.heal.riverbed.schedulers.MetricsScheduler" level="INFO" additivity="false">
        <appender-ref ref="STATS_FILE"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="CORE_FILE"/>
    </root>

</configuration>
