
<report>

    <info error_text="" id="321291" name="" percent="100" remaining_seconds="0" run_time="1744137759" saved="false" size="56" status="completed" template_id="184" user_id="57"/>

    <queries>
        <query actual_end_time="1739369460" actual_start_time="1739365800" data_size="10">
            <granularities>
                <value>ifaces-appd/1min</value>
            </granularities>
            <totals>
                <value></value>
                <value></value>
                <value>1969.11202186</value>
            </totals>
            <data>
                <row>
                    <value>tcp|9090|SSL</value>
                    <value>0.124</value>
                    <value>637.173770492</value>
                </row>
                <row>
                    <value>tcp|389|active_directory</value>
                    <value>0.089</value>
                    <value>453.716120219</value>
                </row>
                <row>
                    <value>tcp|443|SSL</value>
                    <value>0.084</value>
                    <value>429.247540984</value>
                </row>
                <row>
                    <value>tcp|9081|SSL</value>
                    <value>0.039</value>
                    <value>197.70273224</value>
                </row>
                <row>
                    <value>tcp|389|TCP_Unknown</value>
                    <value>0.02</value>
                    <value>104.403825137</value>
                </row>
                <row>
                    <value>tcp|9090|TCP_Unknown</value>
                    <value>0.017</value>
                    <value>85.1573770492</value>
                </row>
                <row>
                    <value>udp|161|UDP_Unknown</value>
                    <value>0.004</value>
                    <value>22.3707650273</value>
                </row>
                <row>
                    <value>tcp|3268|TCP_Unknown</value>
                    <value>0.003</value>
                    <value>15.3355191257</value>
                </row>
                <row>
                    <value>tcp|3268|active_directory</value>
                    <value>0.002</value>
                    <value>8.48715846995</value>
                </row>
                <row>
                    <value>tcp|9090|binary_over_http</value>
                    <value>0.001</value>
                    <value>6.32076502732</value>
                </row>
            </data>
            <columns>
                <column area="none" available="true" category="key" cli_srv="none" comparison="none" comparison_parameter="" context="false" direction="none" has_others="false" id="17" internal="false" metric="none" name="Application" name_type="string" rate="none" role="none" severity="none" sortable="false" statistic="none" strid="ID_APP_NAME" type="app" unit="none"/>
                <column area="none" available="true" category="info" cli_srv="none" comparison="none" comparison_parameter="" context="false" direction="none" has_others="false" id="19" internal="false" metric="none" name="Port Name" name_type="string" rate="none" role="none" severity="none" sortable="false" statistic="none" strid="ID_PROTOPORT_NAME" type="string" unit="none"/>
                <column area="none" available="true" category="key" cli_srv="none" comparison="none" comparison_parameter="" context="false" direction="none" has_others="false" id="3" internal="false" metric="none" name="Protocol" name_type="string" rate="none" role="none" severity="none" sortable="false" statistic="none" strid="ID_PROTOCOL" type="protocol" unit="none"/>
                <column area="none" available="true" category="key" cli_srv="none" comparison="none" comparison_parameter="" context="false" direction="none" has_others="false" id="55" internal="false" metric="none" name="Port #" name_type="string" rate="none" role="none" severity="none" sortable="false" statistic="none" strid="ID_PORT" type="port" unit="none"/>
                <column area="none" available="true" category="compound_key" cli_srv="none" comparison="none" comparison_parameter="" context="false" direction="none" has_others="false" id="21" internal="false" metric="none" name="Port-Application" name_type="string" rate="none" role="none" severity="none" sortable="false" statistic="none" strid="ID_PROTOPORTAPP_PARTS" type="protoportapp_parts" unit="none"/>
                <column area="none" available="true" category="data" cli_srv="none" comparison="none" comparison_parameter="" context="false" direction="none" has_others="false" id="30" internal="false" metric="net_bw" name="Total Bytes" name_type="colname_parts" rate="count" role="none" severity="none" sortable="true" statistic="total" strid="ID_TOTAL_BYTES" type="int" unit="bytes"/>
                <column area="none" available="true" category="data" cli_srv="none" comparison="none" comparison_parameter="" context="false" direction="none" has_others="false" id="33" internal="false" metric="net_bw" name="Avg Bytes/s" name_type="colname_parts" rate="persec" role="none" severity="none" sortable="true" statistic="avg" strid="ID_AVG_BYTES" type="float" unit="bytes"/>
                <column area="none" available="true" category="data" cli_srv="none" comparison="none" comparison_parameter="" context="false" direction="none" has_others="false" id="57" internal="false" metric="net_bw" name="Avg % Util" name_type="colname_parts" rate="pct" role="none" severity="none" sortable="true" statistic="avg" strid="ID_AVG_UTIL" type="float" unit="util"/>
            </columns>
        </query>
    </queries>

</report>