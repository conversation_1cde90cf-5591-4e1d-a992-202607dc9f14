package com.heal.riverbed.parser;

import com.heal.riverbed.pojos.*;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Unmarshaller;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.sql.rowset.spi.XmlReader;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@SpringBootTest
public class XmlParserTest {

    @Autowired
    XmlParser parser;

    @Test
    public  void testXML() throws Exception {
        String xmlResponse = readXmlFromResource("riverbed-response.xml");
        List<Integer> columns = new ArrayList<>();
        columns.add(1);
        columns.add(2);
        columns.add(3);
        Map<Integer, String> columnMap = new HashMap<>();

        columnMap.put(1, "AVG_BYTES");
        columnMap.put(2, "AVG_BYTES");
        columnMap.put(3, "Avg Util");

        Map<String, Map<String, Double>> kpiData = buildKpiRecords(xmlResponse, 0, columnMap, "Inst1");
        assertEquals(0, kpiData.size());

        assertNotNull(xmlResponse);

    }

    public String readXmlFromResource(String fileName) {
        ClassLoader classLoader = XmlParserTest.class.getClassLoader();
        try (InputStream inputStream = classLoader.getResourceAsStream(fileName)) {
            if (inputStream == null) {
                throw new IllegalArgumentException("File not found: " + fileName);
            }

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                return reader.lines().collect(Collectors.joining("\n"));
            }

        } catch (IOException e) {
            throw new RuntimeException("Error reading file: " + fileName, e);
        }
    }

    private Map<String, Map<String, Double>> pivotMap(Map<String, Map<String, Double>> kpiDataMap) {
        Map<String, Map<String, Double>> kpiToAttributes = new HashMap<>();
        for (Map.Entry<String, Map<String, Double>> attributeEntry : kpiDataMap.entrySet()) {
            String attribute = attributeEntry.getKey();
            Map<String, Double> kpis = attributeEntry.getValue();

            for (Map.Entry<String, Double> kpiEntry : kpis.entrySet()) {
                String kpi = kpiEntry.getKey();
                Double value = kpiEntry.getValue();

                kpiToAttributes
                        .computeIfAbsent(kpi, k -> new HashMap<>())
                        .put(attribute, value);
            }
        }

        return kpiToAttributes;
    }

    private Map<String, Map<String, Double>> buildKpiRecords(String xmlResponse, int attributeIndex, Map<Integer, String> columnMap, String instanceIdentifier) {
        Map<String, Map<String, Double>> kpiDataMap = new HashMap<>();
        if (xmlResponse == null || xmlResponse.isEmpty()) {
            return Collections.emptyMap();
        }

        try {

            JAXBContext jaxbContext = JAXBContext.newInstance(Report.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            Report report = (Report) unmarshaller.unmarshal(new StringReader(xmlResponse));

            if (report.getQueries() == null || report.getQueries().isEmpty()) {
                return Collections.emptyMap();
            }

            List<Row> rows = report.getQueries().get(0).getData().getRows();
            if (rows == null || rows.isEmpty()) {
                return Collections.emptyMap();
            }

            for (Row row : rows) {
                List<String> values = row.getValues();
                if (values == null || values.isEmpty()) {
                    continue;
                }

                String attributeValue = values.get(attributeIndex);

                Map<String, Double> kpiMap = new HashMap<>();
                columnMap.forEach((index, kpiName) -> {
                    if(index < 0 || index >= values.size()) {
                        return;
                    }

                    Double kpiValue = parseDoubleSafe(values.get(index));
                    if(kpiValue == null) {
                        return;
                    }
                    kpiMap.put(kpiName, kpiValue);
                });
                kpiDataMap.put(attributeValue, kpiMap);
            }
        } catch (Exception e) {

        }
        return kpiDataMap;
    }


    private Double parseDoubleSafe(String text) {
        try {
            return Double.valueOf(text);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
