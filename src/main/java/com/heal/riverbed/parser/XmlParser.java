package com.heal.riverbed.parser;

import com.heal.riverbed.pojos.*;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Unmarshaller;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.StringReader;
import java.util.*;

@Slf4j
@Service
public class XmlParser {

    public Map<String, Map<String, Double>> buildKpiRecords(String xmlResponse, int attributeIndex, Map<Integer, String> columnMap,
                                                            String instanceIdentifier) {
        log.trace("Inside buildKpiRecords(instanceIdentifier:{}, attributeIndex:{}, columnMap:{}) method", instanceIdentifier, attributeIndex, columnMap);
        Map<String, Map<String, Double>> kpiDataMap = new HashMap<>();
        if (xmlResponse == null || xmlResponse.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(Report.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            Report report = (Report) unmarshaller.unmarshal(new StringReader(xmlResponse));

            if (report.getQueries() == null || report.getQueries().isEmpty()) {
                log.error("No queries output in response for instanceIdentifier:{}", instanceIdentifier);
                return Collections.emptyMap();
            }

            log.info("Queries size:{} for instanceIdentifier:{}", report.getQueries().size(), instanceIdentifier);
            List<Row> rows = report.getQueries().get(0).getData().getRows();
            if (rows == null || rows.isEmpty()) {
                log.error("No rows output in response for instanceIdentifier:{}", instanceIdentifier);
                return Collections.emptyMap();
            }

            log.info("Number of rows:{} for instanceIdentifier:{}", rows.size(), instanceIdentifier);
            for (Row row : rows) {
                List<String> values = row.getValues();
                if (values == null || values.isEmpty()) {
                    continue;
                }

                String attribute = values.get(attributeIndex);

                Map<String, Double> kpiMap = new HashMap<>();
                columnMap.forEach((index, kpiName) -> {
                    if (index < 0 || index >= values.size()) {
                        log.error("Index does not exists to extract kpi value for attribute:{}, kpiName:{}, index:{}",
                                attribute, kpiName, index);
                        return;
                    }

                    String kpiValueStr = values.get(index);
                    if (kpiValueStr == null) {
                        log.error("Kpi value does not exists for attribute:{}, kpiName:{}, index:{}",
                                attribute, kpiName, index);
                        return;
                    }

                    Double kpiValue = parseDoubleSafe(kpiValueStr);
                    if (kpiValue == null) {
                        log.error("Could not convert the value to double for attribute:{}, kpiName:{}, value:{}",
                                attribute, kpiName, kpiValueStr);
                        return;
                    }
                    kpiMap.put(kpiName, kpiValue);
                });
                kpiDataMap.put(attribute, kpiMap);
            }
        } catch (Exception e) {
            log.error("Error occurred while processing the xml response to kpi data for instanceIdentifier:{}", instanceIdentifier, e);
        }
        return kpiDataMap;
    }

    private Double parseDoubleSafe(String text) {
        try {
            return Double.valueOf(text);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public Map<String, Map<String, Double>> pivotMap(Map<String, Map<String, Double>> kpiDataMap, String srcInstIdentifier) {
        log.trace("Inside pivotMap(srcInstIdentifier:{}) method", srcInstIdentifier);
        Map<String, Map<String, Double>> kpiToAttributes = new HashMap<>();
        for (Map.Entry<String, Map<String, Double>> attributeEntry : kpiDataMap.entrySet()) {
            String attribute = attributeEntry.getKey();
            Map<String, Double> kpis = attributeEntry.getValue();

            for (Map.Entry<String, Double> kpiEntry : kpis.entrySet()) {
                String kpi = kpiEntry.getKey();
                Double value = kpiEntry.getValue();

                kpiToAttributes
                        .computeIfAbsent(kpi, k -> new HashMap<>())
                        .put(attribute, value);
            }
        }

        return kpiToAttributes;
    }

    public Map<Integer, String> getKpiColumnIndex(String scrInstIdentifier, String parameter) {
        Map<Integer, String> resultMap = new HashMap<>();
        // Split by comma
        String[] entries = parameter.split(",");

        for (String entry : entries) {
            String[] parts = entry.trim().split("#");
            if (parts.length == 2) {
                try {
                    Integer key = Integer.valueOf(parts[0].trim());
                    String value = parts[1].trim();
                    resultMap.put(key, value);
                } catch (NumberFormatException e) {
                    log.error("Error occurred while converting into parameter:{}, scrInstIdentifier:{}", parameter, scrInstIdentifier);
                }
            }
        }

        return resultMap;
    }

    public String prepareRequestPayload(String entity, String columns, String inf, String gpb, String limit, String resolution, long from, long to, String template) {
        Date startDate = new Date(from);
        Date endDate = new Date(to);
        Map<String, Object> placeholders = Map.of(
                "${startTime}", startDate.toInstant().getEpochSecond(),
                "${endTime}", endDate.toInstant().getEpochSecond(),
                "${resolution}", resolution,
                "${branchIp}", entity,
                "${interface}", inf,
                "${groupBy}", gpb,
                "${limit}", limit,
                "${columns}", columns
        );
        for (Map.Entry<String, Object> entry : placeholders.entrySet()) {
            template = template.replace(entry.getKey(), entry.getValue().toString());
        }
        log.debug("Riverbed request payload : {}, branch : {}", template, entity);
        return template;
    }

}
