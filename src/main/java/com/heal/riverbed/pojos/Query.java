package com.heal.riverbed.pojos;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@XmlAccessorType(XmlAccessType.FIELD)
public class Query {

    @XmlAttribute(name = "actual_start_time")
    private String actualStartTime;

    @XmlAttribute(name = "actual_end_time")
    private String actualEndTime;

    @XmlAttribute(name = "data_size")
    private String dataSize;

    @XmlElement(name = "granularities")
    private Granularities granularities;

    @XmlElement(name = "totals")
    private Totals totals;

    @XmlElement(name = "columns")
    private Columns columns;

    @XmlElement(name = "data")
    private DataItem data;
}
