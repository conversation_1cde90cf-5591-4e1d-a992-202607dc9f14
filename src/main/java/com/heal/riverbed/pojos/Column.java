package com.heal.riverbed.pojos;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@XmlAccessorType(XmlAccessType.FIELD)
public class Column {

    @XmlAttribute
    private String area;

    @XmlAttribute
    private boolean available;

    @XmlAttribute
    private String category;

    @XmlAttribute(name = "cli_srv")
    private String cliSrv;

    @XmlAttribute
    private String comparison;

    @XmlAttribute(name = "comparison_parameter")
    private String comparisonParameter;

    @XmlAttribute
    private boolean context;

    @XmlAttribute
    private String direction;

    @XmlAttribute(name = "has_others")
    private boolean hasOthers;

    @XmlAttribute
    private int id;

    @XmlAttribute
    private boolean internal;

    @XmlAttribute
    private String metric;

    @XmlAttribute
    private String name;

    @XmlAttribute(name = "name_type")
    private String nameType;

    @XmlAttribute
    private String rate;

    @XmlAttribute
    private String role;

    @XmlAttribute
    private String severity;

    @XmlAttribute
    private boolean sortable;

    @XmlAttribute
    private String statistic;

    @XmlAttribute
    private String strid;

    @XmlAttribute
    private String type;

    @XmlAttribute
    private String unit;
}