package com.heal.riverbed.pojos;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@XmlAccessorType(XmlAccessType.FIELD)
public class Info {

    @XmlAttribute(name = "id")
    private String id;

    @XmlAttribute(name = "name")
    private String name;

    @XmlAttribute(name = "percent")
    private String percent;

    @XmlAttribute(name = "remaining_seconds")
    private String remainingSeconds;

    @XmlAttribute(name = "run_time")
    private String runTime;

    @XmlAttribute(name = "saved")
    private String saved;

    @XmlAttribute(name = "size")
    private String size;

    @XmlAttribute(name = "status")
    private String status;

    @XmlAttribute(name = "template_id")
    private String templateId;

    @XmlAttribute(name = "user_id")
    private String userId;
}
