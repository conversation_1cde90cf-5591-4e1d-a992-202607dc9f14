package com.heal.riverbed.pojos;

import jakarta.xml.bind.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@XmlRootElement(name = "report")
@XmlAccessorType(XmlAccessType.FIELD)
public class Report {

    @XmlElement(name = "info")
    private Info info;

    @XmlElementWrapper(name = "queries")
    @XmlElement(name = "query")
    private List<Query> queries;
}
