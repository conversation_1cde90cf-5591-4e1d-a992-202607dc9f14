package com.heal.riverbed;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableAsync;

@Slf4j
@EnableAsync
@ComponentScan("com.heal")
@PropertySource(value = "classpath:conf.properties")
@SpringBootApplication
public class RiverbedMain {

    public static void main(String[] args) {
        SpringApplication.run(RiverbedMain.class, args);
    }
}
