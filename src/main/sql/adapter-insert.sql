INSERT INTO `appsone`.`mst_sub_type` (`name`, `mst_type_id`, `created_time`, `updated_time`, `user_details_id`, `account_id`, `description`, `is_custom`, `status`) VALUES ('RiverbedKpi', '114', '2023-12-18 16:30:00', '2023-12-18 16:30:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Riverbed KPI collection', '0', '1');

INSERT INTO `appsone`.`mst_type` (`type`, `description`, `created_time`, `updated_time`, `user_details_id`, `account_id`, `status`) VALUES ('ConnectorWorkerType', 'Connector worker type', '2025-01-03 00:00:00', '2025-01-03 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', '1');

INSERT INTO `appsone`.`mst_sub_type` (`name`, `mst_type_id`, `created_time`, `updated_time`, `user_details_id`, `account_id`, `description`, `is_custom`, `status`) VALUES ('Extractor', (select id from mst_type where type='ConnectorWorkerType'), '2025-01-06 00:00:00', '2025-01-06 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Connector extractor', '0', '1');
INSERT INTO `appsone`.`mst_sub_type` (`name`, `mst_type_id`, `created_time`, `updated_time`, `user_details_id`, `account_id`, `description`, `is_custom`, `status`) VALUES ('Transformer', (select id from mst_type where type='ConnectorWorkerType'), '2025-01-06 00:00:00', '2025-01-06 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Connector transformer', '0', '1');
INSERT INTO `appsone`.`mst_sub_type` (`name`, `mst_type_id`, `created_time`, `updated_time`, `user_details_id`, `account_id`, `description`, `is_custom`, `status`) VALUES ('Loader', (select id from mst_type where type='ConnectorWorkerType'), '2025-01-06 00:00:00', '2025-01-06 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Connector loader', '0', '1');

## TODO: Need to change the account_id according to the implementation
INSERT INTO `appsone`.`scheduler_details` (`name`, `type_id`, `sink_type_id`, `start_time`, `cron_expression`, `created_time`, `updated_time`, `user_details_id`, `account_id`, `status`) VALUES ('Riverbed_KPI', '340', '373', '2025-02-12 06:45:07', '* * * * * ? *', '2025-02-12 06:45:07', '2025-02-12 06:45:07', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2', '1');

INSERT INTO `appsone`.`scheduler_arguments` (`argument_name`, `argument_value`, `default_value`, `is_placeholder`, `scheduler_id`, `created_time`, `updated_time`, `user_details_id`) VALUES ('queue_name', 'riverbed-connector-1', 'riverbed-connector-1', '0', (select id from scheduler_details where name='Riverbed_KPI'), '2025-02-12 06:45:07', '2025-02-12 06:45:07', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `appsone`.`scheduler_arguments` (`argument_name`, `argument_value`, `default_value`, `is_placeholder`, `scheduler_id`, `created_time`, `updated_time`, `user_details_id`) VALUES ('connector_instance', 'RB-1', 'RB-1', '0', (select id from scheduler_details where name='Riverbed_KPI'), '2025-02-12 06:45:07', '2025-02-12 06:45:07', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `appsone`.`scheduler_arguments` (`argument_name`, `argument_value`, `default_value`, `is_placeholder`, `scheduler_id`, `created_time`, `updated_time`, `user_details_id`) VALUES ('connector_chain', 'Riverbed_KPIs', 'Riverbed_KPIs', '0', (select id from scheduler_details where name='Riverbed_KPI'), '2025-02-12 06:45:07', '2025-02-12 06:45:07', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO `appsone`.`scheduler_job_details` (`name`, `scheduler_id`, `implementation_id`, `created_time`, `updated_time`, `user_details_id`, `status`, `job_status`) VALUES ('Riverbed_KPIs', (select id from scheduler_details where name='Riverbed_KPI'), (select id from mst_sub_type where name='RiverbedKpi'), '2025-02-12 06:45:07', '2025-02-12 06:45:07', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'SCHEDULED');


INSERT INTO appsone.connector_chains (`chain_identifier`, `status`) VALUES ("Riverbed_KPIs", 1);

## TODO: Need to change the account_id according to the implementation

INSERT INTO `appsone`.`mst_connector_instance_details` (`account_id`, `identifier`, `mst_connector_details_id`, `connector_chain_id`) VALUES ('2', 'RB-1', '13', (select id from connector_chain where chain_identifier = 'Riverbed_KPIs'));


#KPI
INSERT INTO appsone.connector_workers (`class_path`, `worker_type_id`) VALUES ('com.heal.riverbed.extractor.RiverbedKpisExtractor', (select id from mst_sub_type where name='Extractor'));
INSERT INTO appsone.connector_workers (`class_path`, `worker_type_id`) VALUES ('com.heal.etladapter.transformers.HealKPITransformer', (select id from mst_sub_type where name='Transformer'));
INSERT INTO appsone.connector_workers (`class_path`, `worker_type_id`) VALUES ('com.heal.etladapter.loaders.HealKPIHttpLoader', (select id from mst_sub_type where name='Loader'));

INSERT INTO appsone.connector_chain_worker_mapping (`connector_chain_id`, `connector_worker_id`, `status`, `re_initialize`, `order`, `created_time`, `updated_time`, `user_details_id`) VALUES ((select id from appsone.connector_chains where chain_identifier='Riverbed_KPIs'), (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'), 1, 1, 1, '2025-03-13 00:00:00', '2025-03-13 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO appsone.connector_chain_worker_mapping (`connector_chain_id`, `connector_worker_id`, `status`, `re_initialize`, `order`, `created_time`, `updated_time`, `user_details_id`) VALUES ((select id from appsone.connector_chains where chain_identifier='Riverbed_KPIs'), (select id from appsone.connector_workers where class_path='com.heal.etladapter.transformers.HealKPITransformer'), 1, 1, 2, '2025-03-13 00:00:00', '2025-03-13 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO appsone.connector_chain_worker_mapping (`connector_chain_id`, `connector_worker_id`, `status`, `re_initialize`, `order`, `created_time`, `updated_time`, `user_details_id`) VALUES ((select id from appsone.connector_chains where chain_identifier='Riverbed_KPIs'), (select id from appsone.connector_workers where class_path='com.heal.etladapter.loaders.HealKPIHttpLoader'), 1, 1, 2, '2025-03-13 00:00:00', '2025-03-13 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

##KPI
## TODO: Need to change the  url, Authorization, payload etc.. values according to the implementation
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  values('riverbed.url','https://10.251.56.101/api/profiler/1.17/reporting/reports/synchronous',(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  values('riverbed.authorization','<token>',(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  values('domain','riverbed',(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  values("riverbed.request.payload", '{"criteria":{"time_frame":{"start":${startTime},"end":${endTime},"resolution":"${resolution}"},"queries":[{"sort_column":33,"realm":"traffic_summary","traffic_expression":"interface ${branchIp}${interface}","group_by":"${groupBy}","limit":${limit},"columns":${columns}}]},"template_id":184}',(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('types', 'apt#:1,apt#:2,hos#:1,hos#:2', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('apt_:1_attrIndex', '0', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('apt_:2_attrIndex', '0', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('hos_:1_attrIndex', '0', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('hos_:2_attrIndex', '0', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('apt_:1_limit', '10', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('apt_:2_limit', '10', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('hos_:1_limit', '10', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('hos_:2_limit', '10', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('apt_:1_kpis', '1#Port_INF1_Avg_Util, 2#Port_INF1_Avg_Bytes, 3#Port_INF1_Total_Bytes', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('apt_:2_kpis', '1#Port_INF2_Avg_Util, 2#Port_INF2_Avg_Bytes, 3#Port_INF2_Total_Bytes', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('hos_:1_kpis', '1#Host_INF1_Avg_Util, 2#Host_INF1_Avg_Bytes, 3#Host_INF1_Total_Bytes, 4#Host_INF1_Total_Packets, 5#Host_INF1_Avg_Packets', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('hos_:2_kpis', '1#Host_INF2_Avg_Util, 2#Host_INF2_Avg_Bytes, 3#Host_INF2_Total_Bytes, 4#Host_INF2_Total_Packets, 5#Host_INF2_Avg_Packets', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('apt_:1_resolution', '1min', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('apt_:2_resolution', '1min', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('hos_:1_resolution', '1min', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('hos_:2_resolution', '1min', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('apt_:1_columns', '[6,57,33,34]', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('apt_:2_columns', '[6,57,33,34]', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('hos_:1_columns', '[6,57,33,34,31,30]', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('hos_:2_columns', '[6,57,33,34,31,30]', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) VALUES ('riverbed.entity.types', 'HOST', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedKpisExtractor'));


insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  values('domain','riverbed',(select id from appsone.connector_workers where class_path='com.heal.etladapter.transformers.HealKPITransformer'));
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  values('default.instance','DEFAULT-INSTANCE',(select id from appsone.connector_workers where class_path='com.heal.etladapter.transformers.HealKPITransformer'));
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  values('default.agent','DEFAULT-AGENT',(select id from appsone.connector_workers where class_path='com.heal.etladapter.transformers.HealKPITransformer'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  values('data.receiver.kpi.endpoint','https://haproxy.appnomic:9998/raw-agents-kpi-data',(select id from appsone.connector_workers where class_path='com.heal.etladapter.loaders.HealKPIHttpLoader'));
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  values('domain','riverbed',(select id from appsone.connector_workers where class_path='com.heal.etladapter.loaders.HealKPIHttpLoader'));


INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Port_INF1_Avg_Util', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Port_INF1_Avg_Bytes', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Port_INF1_Total_Bytes', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Port_INF2_Avg_Util', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Port_INF2_Avg_Bytes', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Port_INF2_Total_Bytes', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Host_INF1_Avg_Util', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Host_INF1_Avg_Bytes', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Host_INF1_Total_Bytes', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Host_INF1_Total_Packets', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Host_INF1_Avg_Packets', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Host_INF2_Avg_Util', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Host_INF2_Total_Bytes', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Host_INF2_Total_Packets', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Host_INF2_Avg_Packets', 'riverbed');
INSERT INTO `appsone`.`domain_metric` (`entity_type`, `metric_identifier`, `domain`) VALUES ('HOST', 'Host_INF2_Avg_Bytes', 'riverbed');


INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Host_INF1_Avg_Util', 'Host_INF1_Avg_Util');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Host_INF1_Avg_Bytes', 'Host_INF1_Avg_Bytes');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Host_INF1_Total_Bytes', 'Host_INF1_Total_Bytes');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Host_INF1_Total_Packets', 'Host_INF1_Total_Packets');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Host_INF1_Avg_Packets', 'Host_INF1_Avg_Packets');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Host_INF2_Avg_Util', 'Host_INF2_Avg_Util');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Host_INF2_Total_Bytes', 'Host_INF2_Total_Bytes');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Host_INF2_Total_Packets', 'Host_INF2_Total_Packets');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Host_INF2_Avg_Packets', 'Host_INF2_Avg_Packets');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Host_INF2_Avg_Bytes', 'Host_INF2_Avg_Bytes');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Port_INF1_Avg_Util', 'Port_INF1_Avg_Util');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Port_INF1_Avg_Bytes', 'Port_INF1_Avg_Bytes');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Port_INF1_Total_Bytes', 'Port_INF1_Total_Bytes');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Port_INF2_Avg_Util', 'Port_INF2_Avg_Util');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Port_INF2_Avg_Bytes', 'Port_INF2_Avg_Bytes');
INSERT INTO `appsone`.`domain_to_heal_kpi_mappings` (`domain`, `heal_kpi_identifier`, `src_kpi_identifier`) VALUES ('riverbed', 'Port_INF2_Total_Bytes', 'Port_INF2_Total_Bytes');

INSERT INTO `appsone`.`domain_entities` (`entity_identifier`, `entity_type`, `domain`) VALUES ('************', 'HOST', 'riverbed');
INSERT INTO `appsone`.`domain_entities` (`entity_identifier`, `entity_type`, `domain`) VALUES ('************', 'HOST', 'riverbed');

INSERT INTO `appsone`.`domain_entity_heal_instance_mapper` (`domain_instance_name`, `heal_agent_uid`, `heal_instance_name`, `heal_service_name`, `domain`) VALUES ('************', 'riverbed_agent', '************_HOST', 'riverbed_service_1', 'riverbed');
INSERT INTO `appsone`.`domain_entity_heal_instance_mapper` (`domain_instance_name`, `heal_agent_uid`, `heal_instance_name`, `heal_service_name`, `domain`) VALUES ('************', 'riverbed_agent', '************_HOST', 'riverbed_service_1', 'riverbed');
