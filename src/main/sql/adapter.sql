
create table `domain_entity_heal_instance_mapper`
(
    `id` int not null auto_increment primary key,
    `domain_instance_name` varchar(512) not null,
    `heal_agent_uid` varchar(1024) not null,
    `heal_instance_name` varchar(1024) not null,
    `heal_service_name` varchar(1024) not null,
     `domain` varchar(256) not null
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `domain_to_heal_kpi_mappings` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `domain` varchar(256) NOT NULL,
  `heal_kpi_identifier` varchar(100) NOT NULL,
  `src_kpi_identifier` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

create table `domain_entities` (
`id` int not null auto_increment primary key,
`entity_identifier` varchar(256) not null,
`entity_type` varchar(256) not null,
`domain` varchar(256) not null
)ENGINE=InnoDB DEFAULT CHARSET=latin1;

create table `domain_metric` (
`id` int not null auto_increment primary key,
`entity_type` varchar(256) not null,
`metric_identifier` varchar(256) not null,
`domain` varchar(256) not null
)ENGINE=InnoDB DEFAULT CHARSET=latin1;